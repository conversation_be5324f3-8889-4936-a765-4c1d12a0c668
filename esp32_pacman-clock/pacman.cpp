#include "pacman.h"

Pacman::<PERSON>man(int x, int y) {
  _x = x;
  _y = y;
}

void Pacman::turn(Direction dir) {
  // set to right  
  memcpy( _PACMAN, PACMAN_CONST, sizeof(_PACMAN) );
  
  if (dir == Direction::LEFT) {
    flip();
  } else if (dir == Direction::DOWN) {
    flip();
    rotate();    
    flip();
  } else if (dir == Direction::UP) {
    rotate();
  }

  changePacmanColor(current_color);
  _direction = dir;
}

void Pacman::move(Direction dir) {
  if (dir == Direction::RIGHT) {
    _x += PACMAN_PACE;
  } else if (dir == Direction::LEFT) {
    _x -= PACMAN_PACE;
  } else if (dir == Direction::DOWN) {
    _y += PACMAN_PACE;
  } else if (dir == Direction::UP) {
    _y -= PACMAN_PACE;
  }
}

void Pacman::jump() {
  if (_state != INVENCIBLE && (millis() - lastMillis > 500) ) {
    _lastState = _state;
    _state = INVENCIBLE;

    Locator::getDisplay()->fillRect(_x, _y, _width, _height, 0x0000);
    
    _width = PACMAN_JUMP_SIZE[0];
    _height = PACMAN_JUMP_SIZE[1];

    direction = UP;

    _lastY = _y;
    _lastX = _x;
  }  
}

void Pacman::idle() {
  if (_state != STOPPED) {
    _lastState = _state;
    _state = STOPPED;

    Locator::getDisplay()->fillRect(_x, _y, _width, _height, 0x0000);

    _width = PACMAN_IDLE_SIZE[0];
    _height = PACMAN_IDLE_SIZE[1];
  }
}

void Pacman::init() {
  Locator::getEventBus()->subscribe(this);
  Locator::getDisplay()->drawRGBBitmap(_x, _y, _PACMAN[int(_pacman_anim)], SPRITE_SIZE, SPRITE_SIZE);
}

void Pacman::update() { 
  if (_state == MOVING || _state == INVENCIBLE) {
    Locator::getDisplay()->fillRect(_x,_y, SPRITE_SIZE, SPRITE_SIZE, 0);
    this->move(_direction);
  }

  if (_iteration % 3 == 0)
    _pacman_anim = !_pacman_anim; 

  if (_state == INVENCIBLE) {
    if (_iteration % 2 == 0) {
      current_color = random(LONG_MAX);
    } else {
      current_color = 0xFE40;
    }
    
    if ((millis() - invencibleTimeout) >= 7000) {
      _state = MOVING;
      current_color = 0xFE40;
    }

    changePacmanColor(current_color);
  }
  
  Locator::getDisplay()->drawRGBBitmap(_x, _y, _PACMAN[int(_pacman_anim)], SPRITE_SIZE, SPRITE_SIZE);
  _iteration++;
}

void Pacman::setState(Pacman::State state) {
  if (state == INVENCIBLE) {
    invencibleTimeout = millis();
    randomSeed(millis());
  }
  _state = state;
}

void Pacman::rotate() {
  int n = SPRITE_SIZE;
  uint16_t temp0[2][25] = {};  
  memcpy( temp0, _PACMAN, sizeof(temp0) );
 
  for(int i = 0; i < n; i++) {
    for(int j = 0; j < n; j++) {
      _PACMAN[0][(i*n)+j] = temp0[0][(n-1-i)+(j*n)];
      _PACMAN[1][(i*n)+j] = temp0[1][(n-1-i)+(j*n)];
    }
  }
}

void Pacman::flip() {
  int n = SPRITE_SIZE;
  for(int i = 0; i < n; i++) {
    for(int j = 0; j < floor(n/2)+1; j++) {
      std::swap(_PACMAN[0][j + (i*n)], _PACMAN[0][(i*n)+n-j-1]);
      std::swap(_PACMAN[1][j + (i*n)], _PACMAN[1][(i*n)+n-j-1]);
    }
  }
}

void Pacman::execute(EventType event, Sprite* caller) {
  if (event == EventType::COLLISION) {
    direction = DOWN;
  }
}

void Pacman::changePacmanColor(uint16_t newcolor) {
  for (int i=0; i < SPRITE_SIZE*SPRITE_SIZE; i++) {
    if (_PACMAN[0][i] != 0x0000) 
      _PACMAN[0][i] = newcolor;
    if (_PACMAN[1][i] != 0x0000)
      _PACMAN[1][i] = newcolor;
  }
}

void Pacman::setColor(uint16_t newColor) {
  current_color = newColor;
  changePacmanColor(current_color);
}

const char* Pacman::name() {
  return "PACMAN";
}
