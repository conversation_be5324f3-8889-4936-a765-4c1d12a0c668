# Pacman Clock Web Server

This ESP32 project now includes a web server that allows you to control the Pacman clock through a web interface.

## Features

- **Color Control**: Change <PERSON><PERSON>'s color and background colors in real-time
- **Brightness Control**: Adjust display brightness using a slider
- **Animation Control**: Toggle Pacman animation on/off
- **Random Colors**: Apply random colors to Pacman and background
- **Reset Function**: Reset all colors to defaults

## Web Interface

The web interface is accessible at the ESP32's IP address (displayed in Serial Monitor on startup).

### Controls Available:
1. **Pacman Colors**: 15 predefined colors for Pacman character
2. **Background Colors**: 4 background color options  
3. **Brightness Slider**: Range 1-255
4. **Control Buttons**:
   - Reset: Restore default colors
   - Toggle Animation: Start/stop Pacman animation
   - Random: Apply random colors

## API Endpoints

- `GET /` - Main web interface
- `POST /api/color` - Change colors (JSON: `{"target": "pacman|background", "color": "#RRGGBB"}`)
- `POST /api/brightness` - Set brightness (JSON: `{"brightness": 0-255}`)
- `POST /api/reset` - Reset to defaults
- `POST /api/toggle-animation` - Toggle animation
- `POST /api/random` - Apply random colors
- `GET /api/status` - Get current status

## Setup

1. Upload the sketch to ESP32
2. Upload the data folder contents to SPIFFS:
   - In Arduino IDE: Tools → ESP32 Sketch Data Upload
   - Or use PlatformIO's "Upload Filesystem Image"
3. Connect to WiFi (uses WiFiConnect library)
4. Open Serial Monitor to see the IP address
5. Navigate to the IP address in your web browser

## File Structure

```
esp32_pacman-clock/
├── data/
│   └── index.html          # Web interface
├── WebServer.h             # Web server class header
├── WebServer.cpp           # Web server implementation
├── pacman.h/cpp           # Pacman sprite with color control
├── Clockface.h/cpp        # Clock display with web integration
└── esp32_pacman-clock.ino # Main sketch
```

## Dependencies

- ESP32-HUB75-MatrixPanel-I2S-DMA
- ArduinoJson
- SPIFFS
- WebServer (built-in ESP32 library)
- WiFi (built-in ESP32 library)

## Troubleshooting

1. **Web page not loading**: Ensure SPIFFS data is uploaded correctly
2. **Colors not changing**: Check Serial Monitor for error messages
3. **WiFi connection issues**: Verify WiFiConnect configuration
4. **Display not updating**: Ensure proper wiring and power to RGB matrix

The web interface is designed to be mobile-friendly and matches the layout shown in the reference screenshot.