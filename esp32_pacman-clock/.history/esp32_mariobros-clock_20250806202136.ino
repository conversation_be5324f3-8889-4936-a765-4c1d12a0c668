//参考文章：https://blog.csdn.net/u011625956/article/details/136828317
//https://www.instructables.com/<PERSON><PERSON>-Clock/
//https://github.com/jnthas/mariobros-clock

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "Clockface.h"
#include "WiFiConnect.h"
#include "CWDateTime.h"

// 🔌 Pin Definitions (ESP32-S3 to HUB75 RGB Matrix)
// ---------------------------
#define R1_PIN 47
#define G1_PIN 1
#define B1_PIN 48
#define R2_PIN 45
#define G2_PIN 2
#define B2_PIN 0  // ⚠️ GPIO0 is BOOT pin, must stay HIGH at boot

#define A_PIN 35
#define B_PIN 41
#define C_PIN 36
#define D_PIN 40
#define E_PIN 42

#define LAT_PIN 39
#define OE_PIN 38
#define CLK_PIN 37

// ---------------------------
// 🧱 Panel Configuration
// ---------------------------
#define PANEL_RES_X 64
#define PANEL_RES_Y 64
#define PANEL_CHAIN 1

MatrixPanel_I2S_DMA *dma_display = nullptr;
WiFiConnect wifi;
CWDateTime cwDateTime;
Clockface *clockface;

uint16_t myBLACK = dma_display->color565(0, 0, 0);
uint16_t myWHITE = dma_display->color565(255, 255, 255);
uint16_t myBLUE = dma_display->color565(0, 0, 255);

byte displayBright = 32;

void displaySetup() {
  HUB75_I2S_CFG mxconfig(
    128,   // module width
    64,   // module height
    1    // Chain length
  );

  mxconfig.gpio.e = 32;
  mxconfig.clkphase = false;

/* 
#if HUB75_BLUE_GREEN_SWAP
  // Swap Blue and Green pins because the panel is RBG instead of RGB.
  mxconfig.gpio.b1 = 26;
  mxconfig.gpio.b2 = 12;
  mxconfig.gpio.g1 = 27;
  mxconfig.gpio.g2 = 13;
#endif
*/

  // Display Setup
  dma_display = new MatrixPanel_I2S_DMA(mxconfig);
  dma_display->begin();
  dma_display->setBrightness8(displayBright);
  dma_display->clearScreen();
  dma_display->fillScreen(myBLACK);
}

void setup() {

  Serial.begin(115200);

  displaySetup();

  clockface = new Clockface(dma_display);

  dma_display->setTextSize(1);
  dma_display->setTextColor(myWHITE);
  dma_display->setCursor(5, 0);
  dma_display->println("CLOCKWISE");
  dma_display->setTextColor(myBLUE);
  dma_display->setCursor(0, 32);
  dma_display->print("connecting...");

  wifi.connect();
  cwDateTime.begin();

  clockface->setup(&cwDateTime);
}

void loop() {
  clockface->update();

}
