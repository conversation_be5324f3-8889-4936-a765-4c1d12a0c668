#pragma once

#include <Arduino.h>

#include "hour_font.h"
#include "picopixel.h"

#include <Adafruit_GFX.h>
#include "Locator.h"
#include "IClockface.h"
#include "CWDateTime.h"

//sprites
#include "pacman.h"

// Simple coordinate struct for BFS
struct Point {
  int x, y;
};

class Clockface: public IClockface {
  private:
    static const int MAP_SIZE = 12;
    Adafruit_GFX* _display;
    CWDateTime* _dateTime;
    bool pacmanState = true;
    bool show_seconds = true;

    const char* _weekDayWords = "SUN\0MON\0TUE\0WED\0THU\0FRI\0SAT\0";
    const char* _monthWords = "JAN\0FEB\0MAR\0APR\0MAY\0JUN\0JUL\0AUG\0SEP\0OCT\0NOV\0DEC\0";
    char weekDayTemp[4]= "\0";
    char monthTemp[4]= "\0";

   // BFS related members
   static const int MAX_QUEUE_SIZE = MAP_SIZE * MAP_SIZE;
   Point queue[MAX_QUEUE_SIZE];
   int queueFront, queueRear;
   bool visited[MAP_SIZE][MAP_SIZE];
   Point parent[MAP_SIZE][MAP_SIZE]; // Stores the predecessor point in the path

    enum MapBlock {
      EMPTY = 0,
      FOOD = 1,
      WALL = 2,
      GATE = 3,
      SUPER_FOOD = 4,
      CLOCK = 5,
      GHOST = 6,
      PACMAN = 7,
      OUT_OF_MAP = 99
    };

    // Map definitions - 5 different layouts
    static const int NUM_MAPS = 5;
    int currentMapIndex = 0;

    const byte _MAP_TEMPLATES[NUM_MAPS][12][12] = {
      // Map 1 - Original Classic Layout
      {
        {4,1,1,1,1,1,7,1,1,1,1,4},
        {1,2,2,1,2,2,2,2,1,2,2,1},
        {1,1,1,1,1,2,2,1,1,1,1,1},
        {2,1,5,5,5,5,5,5,5,5,1,2},
        {2,1,5,5,5,5,5,5,5,5,1,2},
        {3,1,5,5,5,5,5,5,5,5,1,3},
        {3,1,5,5,5,5,5,5,5,5,1,3},
        {2,1,5,5,5,5,5,5,5,5,1,2},
        {2,1,5,5,5,5,5,5,5,5,1,2},
        {1,1,1,1,1,2,2,1,1,1,1,1},
        {1,2,2,1,2,2,2,2,1,2,2,1},
        {4,1,1,1,1,1,1,1,1,1,1,4}
      },
      // Map 2 - Cross Pattern
      {
        {4,1,1,1,1,2,2,1,1,1,1,4},
        {1,2,1,1,1,2,2,1,1,1,2,1},
        {1,1,2,1,1,2,2,1,1,2,1,1},
        {1,1,1,2,5,5,5,5,2,1,1,1},
        {1,1,1,1,5,5,5,5,1,1,1,1},
        {2,2,2,1,5,5,5,5,1,2,2,2},
        {2,2,2,1,5,5,5,5,1,2,2,2},
        {1,1,1,1,5,5,5,5,1,1,1,1},
        {1,1,1,2,5,5,5,5,2,1,1,1},
        {1,1,2,1,1,2,2,1,1,2,1,1},
        {1,2,1,1,1,2,2,1,1,1,2,1},
        {4,1,1,1,1,2,2,1,1,1,1,4}
      },
      // Map 3 - Maze Pattern
      {
        {4,1,2,1,2,1,1,2,1,2,1,4},
        {1,2,1,2,1,2,2,1,2,1,2,1},
        {2,1,2,1,2,2,2,2,1,2,1,2},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {2,1,2,1,5,5,5,5,1,2,1,2},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {2,1,2,1,5,5,5,5,1,2,1,2},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {2,1,2,1,2,2,2,2,1,2,1,2},
        {1,2,1,2,1,2,2,1,2,1,2,1},
        {4,1,2,1,2,1,1,2,1,2,1,4}
      },
      // Map 4 - Corner Focus
      {
        {4,1,1,2,2,2,2,2,2,1,1,4},
        {1,1,2,1,1,1,1,1,1,2,1,1},
        {1,2,1,1,1,1,1,1,1,1,2,1},
        {2,1,1,1,5,5,5,5,1,1,1,2},
        {2,1,1,1,5,5,5,5,1,1,1,2},
        {2,1,1,1,5,5,5,5,1,1,1,2},
        {2,1,1,1,5,5,5,5,1,1,1,2},
        {2,1,1,1,5,5,5,5,1,1,1,2},
        {2,1,1,1,5,5,5,5,1,1,1,2},
        {1,2,1,1,1,1,1,1,1,1,2,1},
        {1,1,2,1,1,1,1,1,1,2,1,1},
        {4,1,1,2,2,2,2,2,2,1,1,4}
      },
      // Map 5 - Spiral Pattern
      {
        {4,1,1,1,1,1,1,1,1,1,1,4},
        {1,2,2,2,2,2,2,2,2,2,2,1},
        {1,2,1,1,1,1,1,1,1,1,2,1},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {1,2,1,2,5,5,5,5,2,1,2,1},
        {1,2,1,1,1,1,1,1,1,1,2,1},
        {1,2,2,2,2,2,2,2,2,2,2,1},
        {4,1,1,1,1,1,1,1,1,1,1,4}
      }
    };

    const byte _MAP_CONST[12][12] = {
      {4,1,1,1,1,1,7,1,1,1,1,4},
      {1,2,2,1,2,2,2,2,1,2,2,1},
      {1,1,1,1,1,2,2,1,1,1,1,1},
      {2,1,5,5,5,5,5,5,5,5,1,2},
      {2,1,5,5,5,5,5,5,5,5,1,2},
      {3,1,5,5,5,5,5,5,5,5,1,3},
      {3,1,5,5,5,5,5,5,5,5,1,3},
      {2,1,5,5,5,5,5,5,5,5,1,2},
      {2,1,5,5,5,5,5,5,5,5,1,2},
      {1,1,1,1,1,2,2,1,1,1,1,1},
      {1,2,2,1,2,2,2,2,1,2,2,1},
      {4,1,1,1,1,1,1,1,1,1,1,4}
    };
    
    byte _MAP[12][12] = {
      {4,1,1,1,1,1,7,1,1,1,1,4},
      {1,2,2,1,2,2,2,2,1,2,2,1},
      {1,1,1,1,1,2,2,1,1,1,1,1},
      {2,1,5,5,5,5,5,5,5,5,1,2},
      {2,1,5,5,5,5,5,5,5,5,1,2},
      {3,1,5,5,5,5,5,5,5,5,1,3},
      {3,1,5,5,5,5,5,5,5,5,1,3},
      {2,1,5,5,5,5,5,5,5,5,1,2},
      {2,1,5,5,5,5,5,5,5,5,1,2},
      {1,1,1,1,1,2,2,1,1,1,1,1},
      {1,2,2,1,2,2,2,2,1,2,2,1},
      {4,1,1,1,1,1,1,1,1,1,1,4}
    };

    const byte MAP_BORDER_SIZE = 2;
    const byte MAP_MIN_POS = 0 + MAP_BORDER_SIZE;
    const byte MAP_MAX_POS = 64 - MAP_BORDER_SIZE;

    // first elem is the size
    const int PACMAN_MOVING_BLOCKS[4] = {3, MapBlock::EMPTY, MapBlock::FOOD, MapBlock::GATE};
    const int PACMAN_BLOCKING_BLOCKS[4] = {3, MapBlock::OUT_OF_MAP, MapBlock::WALL, MapBlock::CLOCK};

    void drawMap();
    Clockface::MapBlock nextBlock(Direction dir);
    Clockface::MapBlock nextBlock();
    void turnRandom();
    int countBlocks(Clockface::MapBlock elem);
    bool contains(int v, const int* values);
    void directionDecision(MapBlock nextBlk, bool moving_axis_x);
    bool isValid(int r, int c); // Check if a cell is valid for BFS traversal
    bool isTarget(int r, int c); // Check if a cell contains food/superfood
    bool findShortestPath(int startX, int startY, Direction& nextMove); // BFS implementation
    void reconstructPath(Point start, Point end, Direction& nextMove); // Determine next move from path
    void updateClock();
    const char* weekDayName(int weekday);
    const char* monthName(int month);
    
  public:
    Clockface(Adafruit_GFX* display);
    void setup(CWDateTime *dateTime);
    void update();
    void setPacmanColor(uint16_t color);
    void setBackgroundColor(uint16_t color);
    void resetMap();
    Pacman* getPacman();
};
