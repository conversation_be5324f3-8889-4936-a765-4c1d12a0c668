<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clock Control</title>
    <style>
        body {
            background-color: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
        }

        h1 {
            margin-bottom: 30px;
            font-size: 24px;
        }

        .color-section {
            margin-bottom: 20px;
        }

        .color-section h2 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #ccc;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .color-btn {
            width: 60px;
            height: 60px;
            border: 2px solid #333;
            border-radius: 50%;
            cursor: pointer;
            transition: transform 0.2s, border-color 0.2s;
        }

        .color-btn:hover {
            transform: scale(1.1);
            border-color: #fff;
        }

        .color-btn:active {
            transform: scale(0.95);
        }

        .control-buttons {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 30px;
        }

        .control-btn {
            padding: 12px 24px;
            background-color: #333;
            color: #fff;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.2s;
        }

        .control-btn:hover {
            background-color: #555;
        }

        .control-btn:active {
            background-color: #222;
        }

        .brightness-control {
            margin: 20px 0;
        }

        .brightness-control input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }

        .status {
            margin-top: 20px;
            padding: 10px;
            background-color: #222;
            border-radius: 5px;
            font-size: 14px;
        }

        .map-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .map-btn {
            padding: 15px 10px;
            background-color: #333;
            color: #fff;
            border: 2px solid #555;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            text-align: center;
            transition: all 0.2s;
            min-height: 60px;
        }

        .map-btn:hover {
            background-color: #555;
            border-color: #777;
        }

        .map-btn.active {
            background-color: #007acc;
            border-color: #0099ff;
        }

        .map-btn small {
            display: block;
            font-size: 11px;
            color: #ccc;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Clock Control</h1>
        
        <div class="color-section">
            <h2>Pacman Colors</h2>
            <div class="color-grid">
                <div class="color-btn" style="background-color: #ff0000;" onclick="changeColor('pacman', '#ff0000')"></div>
                <div class="color-btn" style="background-color: #00ff00;" onclick="changeColor('pacman', '#00ff00')"></div>
                <div class="color-btn" style="background-color: #0000ff;" onclick="changeColor('pacman', '#0000ff')"></div>
                <div class="color-btn" style="background-color: #00ffff;" onclick="changeColor('pacman', '#00ffff')"></div>
                <div class="color-btn" style="background-color: #ff00ff;" onclick="changeColor('pacman', '#ff00ff')"></div>
                
                <div class="color-btn" style="background-color: #ffff00;" onclick="changeColor('pacman', '#ffff00')"></div>
                <div class="color-btn" style="background-color: #ffa500;" onclick="changeColor('pacman', '#ffa500')"></div>
                <div class="color-btn" style="background-color: #ff8000;" onclick="changeColor('pacman', '#ff8000')"></div>
                <div class="color-btn" style="background-color: #8000ff;" onclick="changeColor('pacman', '#8000ff')"></div>
                <div class="color-btn" style="background-color: #ffffff;" onclick="changeColor('pacman', '#ffffff')"></div>
                
                <div class="color-btn" style="background-color: #800000;" onclick="changeColor('pacman', '#800000')"></div>
                <div class="color-btn" style="background-color: #008000;" onclick="changeColor('pacman', '#008000')"></div>
                <div class="color-btn" style="background-color: #000080;" onclick="changeColor('pacman', '#000080')"></div>
                <div class="color-btn" style="background-color: #808000;" onclick="changeColor('pacman', '#808000')"></div>
                <div class="color-btn" style="background-color: #800080;" onclick="changeColor('pacman', '#800080')"></div>
            </div>
        </div>

        <div class="color-section">
            <h2>Background Colors</h2>
            <div class="color-grid">
                <div class="color-btn" style="background-color: #000080;" onclick="changeColor('background', '#000080')"></div>
                <div class="color-btn" style="background-color: #008080;" onclick="changeColor('background', '#008080')"></div>
                <div class="color-btn" style="background-color: #800080;" onclick="changeColor('background', '#800080')"></div>
                <div class="color-btn" style="background-color: #000000;" onclick="changeColor('background', '#000000')"></div>
            </div>
        </div>

        <div class="brightness-control">
            <h2>Brightness</h2>
            <input type="range" id="brightness" min="1" max="255" value="32" oninput="changeBrightness(this.value)">
            <div id="brightness-value">32</div>
        </div>

        <div class="color-section">
            <h2>Map Selection</h2>
            <div class="map-grid">
                <button class="map-btn active" onclick="changeMap(0)" id="map-0">Map 1<br><small>Classic</small></button>
                <button class="map-btn" onclick="changeMap(1)" id="map-1">Map 2<br><small>Cross</small></button>
                <button class="map-btn" onclick="changeMap(2)" id="map-2">Map 3<br><small>Maze</small></button>
                <button class="map-btn" onclick="changeMap(3)" id="map-3">Map 4<br><small>Corner</small></button>
                <button class="map-btn" onclick="changeMap(4)" id="map-4">Map 5<br><small>Spiral</small></button>
            </div>
        </div>

        <div class="control-buttons">
            <button class="control-btn" onclick="resetColors()">Reset Colors</button>
            <button class="control-btn" onclick="toggleAnimation()">Toggle Animation</button>
            <button class="control-btn" onclick="randomColors()">Random Colors</button>
            <button class="control-btn" onclick="resetGame()">Reset Game</button>
        </div>

        <div class="status" id="status">Ready</div>
    </div>

    <script>
        function changeColor(target, color) {
            fetch('/api/color', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    target: target,
                    color: color
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('status').textContent = `Changed ${target} color to ${color}`;
            })
            .catch(error => {
                document.getElementById('status').textContent = 'Error: ' + error.message;
            });
        }

        function changeBrightness(value) {
            document.getElementById('brightness-value').textContent = value;
            
            fetch('/api/brightness', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    brightness: parseInt(value)
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('status').textContent = `Brightness set to ${value}`;
            })
            .catch(error => {
                document.getElementById('status').textContent = 'Error: ' + error.message;
            });
        }

        function resetColors() {
            fetch('/api/reset', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('status').textContent = 'Colors reset to default';
            })
            .catch(error => {
                document.getElementById('status').textContent = 'Error: ' + error.message;
            });
        }

        function toggleAnimation() {
            fetch('/api/toggle-animation', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('status').textContent = 'Animation toggled';
            })
            .catch(error => {
                document.getElementById('status').textContent = 'Error: ' + error.message;
            });
        }

        function randomColors() {
            fetch('/api/random', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('status').textContent = 'Random colors applied';
            })
            .catch(error => {
                document.getElementById('status').textContent = 'Error: ' + error.message;
            });
        }

        function resetGame() {
            fetch('/api/reset-game', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('status').textContent = 'Game reset - all food restored';
            })
            .catch(error => {
                document.getElementById('status').textContent = 'Error: ' + error.message;
            });
        }

        // Update status periodically
        setInterval(function() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    // Update any status information if needed
                })
                .catch(error => {
                    // Handle errors silently
                });
        }, 5000);
    </script>
</body>
</html>