#pragma once

#include <WiFi.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include "Clockface.h"

// Use the correct WebServer for ESP32
#ifdef ESP32
  #include <WebServer.h>
#else
  #include <ESP8266WebServer.h>
  typedef ESP8266WebServer WebServer;
#endif

class ClockWebServer {
private:
    WebServer* server;
    Clockface* clockface;
    
    // Current settings
    uint16_t pacmanColor = 0xFE40;  // Default yellow
    uint16_t backgroundColor = 0x000E;  // Default sky blue
    uint16_t clockColor = 0xFE40;      // Default yellow for hour display
    uint16_t dateColor = 0xAD55;       // Default color for date display
    uint16_t foodColor = 0xB58C;       // Default color for food dots
    uint16_t wallColor = 0x0016;       // Default color for walls
    uint16_t superFoodColor = 0xFBE0;  // Default color for super food
    uint8_t brightness = 32;
    bool animationEnabled = true;
    
    // Helper functions
    uint16_t hexToRgb565(String hexColor);
    String rgb565ToHex(uint16_t color);
    void handleRoot();
    void handleColorChange();
    void handleBrightnessChange();
    void handleReset();
    void handleToggleAnimation();
    void handleRandom();
    void handleResetGame();
    void handleChangeMap();
    void handleStatus();
    void handleNotFound();
    
public:
    ClockWebServer(Clockface* cf);
    void begin();
    void handleClient();
    void setPacmanColor(uint16_t color);
    void setBackgroundColor(uint16_t color);
    void setClockColor(uint16_t color);
    void setDateColor(uint16_t color);
    void setFoodColor(uint16_t color);
    void setWallColor(uint16_t color);
    void setSuperFoodColor(uint16_t color);
    void setBrightness(uint8_t brightness);
    uint16_t getPacmanColor() { return pacmanColor; }
    uint16_t getBackgroundColor() { return backgroundColor; }
    uint16_t getClockColor() { return clockColor; }
    uint16_t getDateColor() { return dateColor; }
    uint16_t getFoodColor() { return foodColor; }
    uint16_t getWallColor() { return wallColor; }
    uint16_t getSuperFoodColor() { return superFoodColor; }
    uint8_t getBrightness() { return brightness; }
    bool isAnimationEnabled() { return animationEnabled; }
};